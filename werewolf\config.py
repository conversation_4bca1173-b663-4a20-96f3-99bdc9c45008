# ===== 狼人杀游戏配置文件 =====

class GameConfig:
    """游戏配置类"""

    def __init__(self):
        # 策略配置
        self.default_strategies = {
            "villager": "rational",    # 平民默认策略
            "seer": "strategic",       # 预言家默认策略
            "witch": "strategic",      # 女巫默认策略
            "wolf": "aggressive"       # 狼人默认策略
        }

        # 混乱模式策略配置（只影响平民和女巫）
        self.chaos_strategies = {
            "villager": "chaos",       # 平民混乱策略
            "seer": "strategic",       # 预言家保持正常策略
            "witch": "chaos",          # 女巫混乱策略
            "wolf": "aggressive"       # 狼人保持原策略
        }

        # 设置混乱模式（通过修改这个数字来控制）
        self.chaos_mode_setting = 1  # 1=关闭混乱模式, 2=启用混乱模式

    def get_strategy(self, role):
        """获取指定角色的策略"""
        if self.chaos_mode_setting == 2 and role in ["villager", "witch"]:
            return self.chaos_strategies.get(role, "rational")
        else:
            return self.default_strategies.get(role, "rational")

    def is_chaos_mode(self):
        """检查是否为混乱模式"""
        return self.chaos_mode_setting == 2

# 全局配置实例
game_config = GameConfig()

# ===== 混乱模式配置说明 =====
# 修改上面的 chaos_mode_setting 数值来控制混乱模式：
# 1 = 关闭混乱模式（默认，常规策略）
# 2 = 启用混乱模式（平民和女巫可在特定条件下跳预言家）
