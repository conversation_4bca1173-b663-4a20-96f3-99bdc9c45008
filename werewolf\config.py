# ===== 狼人杀游戏配置文件 =====
import os

class GameConfig:
    """游戏配置类"""

    def __init__(self):
        # 策略配置
        self.default_strategies = {
            "villager": "rational",    # 平民默认策略
            "seer": "strategic",       # 预言家默认策略
            "witch": "strategic",      # 女巫默认策略
            "wolf": "aggressive"       # 狼人默认策略
        }

        # 混乱模式策略配置（只影响平民和女巫）
        self.chaos_strategies = {
            "villager": "chaos",       # 平民混乱策略
            "seer": "strategic",       # 预言家保持正常策略
            "witch": "chaos",          # 女巫混乱策略
            "wolf": "aggressive"       # 狼人保持原策略
        }

        # 设置混乱模式（通过修改这个数字来控制）
        self.chaos_mode_setting = 1  # 1=关闭混乱模式, 2=启用混乱模式

        # 模型系列配置
        self.supported_model_series = {
            "openai": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"],
            "gemini": ["gemini-pro", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-ultra"],
            "claude": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-3-5-sonnet"]
        }

        # 从环境变量获取模型系列设置，默认为openai
        self.model_series = os.getenv('MODEL_SERIES', 'openai').lower()

        # 验证模型系列是否支持
        if self.model_series not in self.supported_model_series:
            print(f"警告：不支持的模型系列 '{self.model_series}'，将使用默认的 'openai' 系列")
            self.model_series = 'openai'

    def get_strategy(self, role):
        """获取指定角色的策略"""
        if self.chaos_mode_setting == 2 and role in ["villager", "witch"]:
            return self.chaos_strategies.get(role, "rational")
        else:
            return self.default_strategies.get(role, "rational")

    def is_chaos_mode(self):
        """检查是否为混乱模式"""
        return self.chaos_mode_setting == 2

    def get_model_series(self):
        """获取当前使用的模型系列"""
        return self.model_series

    def get_supported_models(self, series=None):
        """获取支持的模型列表"""
        if series is None:
            series = self.model_series
        return self.supported_model_series.get(series, [])

    def is_model_supported(self, model_name, series=None):
        """检查指定模型是否在支持列表中"""
        if series is None:
            series = self.model_series
        supported_models = self.get_supported_models(series)
        return model_name in supported_models

    def validate_model_config(self, model_name):
        """验证模型配置是否有效"""
        if not self.is_model_supported(model_name):
            print(f"警告：模型 '{model_name}' 不在 '{self.model_series}' 系列的支持列表中")
            print(f"支持的 {self.model_series} 模型：{', '.join(self.get_supported_models())}")
            return False
        return True

# 全局配置实例
game_config = GameConfig()

# ===== 配置说明 =====

# 混乱模式配置：
# 修改上面的 chaos_mode_setting 数值来控制混乱模式：
# 1 = 关闭混乱模式（默认，常规策略）
# 2 = 启用混乱模式（平民和女巫可在特定条件下跳预言家）

# 模型系列配置：
# 通过环境变量 MODEL_SERIES 来设置使用的模型系列：
# - openai: 兼容OpenAI API的模型（包括各种第三方提供商）
# - gemini: Google Gemini系列模型
# - claude: Anthropic Claude系列模型
#
# 使用示例：
# export MODEL_SERIES=gemini
# export MODEL_NAME=gemini-1.5-pro
#
# 或者：
# export MODEL_SERIES=claude
# export MODEL_NAME=claude-3-5-sonnet
