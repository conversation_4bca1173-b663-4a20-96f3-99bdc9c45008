"""
安全的模板格式化工具
提供对缺失变量的保护机制，防止KeyError异常
"""

import re
from typing import Dict, Any


class SafeFormatter:
    """安全的字符串格式化器，处理缺失变量"""
    
    def __init__(self, default_value: str = ""):
        """
        初始化安全格式化器
        
        Args:
            default_value: 缺失变量的默认值
        """
        self.default_value = default_value
    
    def format(self, template: str, variables: Dict[str, Any]) -> str:
        """
        安全格式化模板字符串
        
        Args:
            template: 包含{{variable}}占位符的模板字符串
            variables: 变量字典
            
        Returns:
            格式化后的字符串
        """
        # 找到所有的变量占位符
        pattern = r'\{\{(\w+)\}\}'
        
        def replace_var(match):
            var_name = match.group(1)
            if var_name in variables:
                value = variables[var_name]
                # 如果值为None，使用默认值
                if value is None:
                    return self.default_value
                return str(value)
            else:
                # 变量不存在时使用默认值
                return self.default_value
        
        # 替换所有变量
        result = re.sub(pattern, replace_var, template)
        return result


def safe_format_prompt(template: str, variables: Dict[str, Any], default_value: str = "") -> str:
    """
    安全格式化prompt模板的便捷函数
    
    Args:
        template: 模板字符串
        variables: 变量字典
        default_value: 缺失变量的默认值
        
    Returns:
        格式化后的字符串
    """
    formatter = SafeFormatter(default_value)
    return formatter.format(template, variables)


def validate_template_variables(template: str, variables: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证模板变量，返回缺失的变量信息
    
    Args:
        template: 模板字符串
        variables: 变量字典
        
    Returns:
        包含验证结果的字典
    """
    # 找到模板中所有的变量
    pattern = r'\{\{(\w+)\}\}'
    template_vars = set(re.findall(pattern, template))
    
    # 检查提供的变量
    provided_vars = set(variables.keys())
    
    # 找出缺失和多余的变量
    missing_vars = template_vars - provided_vars
    extra_vars = provided_vars - template_vars
    
    return {
        'template_vars': template_vars,
        'provided_vars': provided_vars,
        'missing_vars': missing_vars,
        'extra_vars': extra_vars,
        'is_valid': len(missing_vars) == 0
    }


# 为了兼容现有代码，提供一个包装函数
def format_prompt(template: str, variables: Dict[str, Any]) -> str:
    """
    兼容现有format_prompt调用的安全格式化函数
    
    Args:
        template: 模板字符串
        variables: 变量字典
        
    Returns:
        格式化后的字符串
    """
    return safe_format_prompt(template, variables, default_value="[变量缺失]")
