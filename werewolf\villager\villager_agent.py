from villager.prompt import (
    DESC_PROMPT, VOTE_PROMPT,
    BASE_PERSONA_PROMPT, GAME_RULES_SUMMARY, VILLAGER_ROLE_PROMPT,
    ACTION_TEMPLATES, STRATEGY_TEMPLATES, OUTPUT_TEMPLATES,
    build_prompt
)
from agent_build_sdk.model.roles import ROLE_VILLAGER
from agent_build_sdk.model.werewolf_model import AgentResp, AgentReq, STATUS_START, STATUS_WOLF_SPEECH, \
    STATUS_VOTE_RESULT, STATUS_SKILL, STATUS_SKILL_RESULT, STATUS_NIGHT_INFO, STATUS_DAY, STATUS_DISCUSS, STATUS_VOTE, \
    STATUS_RESULT, STATUS_NIGHT
from agent_build_sdk.utils.logger import logger
from agent_build_sdk.sdk.role_agent import BasicRoleAgent
from common.safe_format import format_prompt


class VillagerAgent(BasicRoleAgent):
    """平民角色Agent"""

    def __init__(self, model_name):
        super().__init__(ROLE_VILLAGER, model_name=model_name)
        # 默认策略类型，可以根据需要动态调整
        self.strategy_type = "rational"  # 可选: "rational", "follower", "skeptical", "observant"

    def set_strategy(self, strategy_type):
        """设置平民的策略类型"""
        if strategy_type in ["rational", "follower", "skeptical", "observant", "chaos"]:
            self.strategy_type = strategy_type
        else:
            logger.warning(f"未知的策略类型: {strategy_type}, 使用默认策略 'rational'")
            self.strategy_type = "rational"

    def perceive(self, req=AgentReq):
        if req.status == STATUS_START:
            self.memory.clear()
            self.memory.set_variable("name", req.name)
            self.memory.append_history("薄伽丘：游戏开始，这是一场6人局狼人杀游戏，包括预言家、女巫、猎人、平民和狼人")
            self.memory.append_history(f"薄伽丘：{req.name}被分配到的角色是[平民]")
        elif req.status == STATUS_NIGHT:
            # 记录当前夜晚轮次
            current_round = getattr(req, 'round', 1)
            self.memory.set_variable("current_night", current_round)
            self.memory.append_history(f"薄伽丘：第{current_round}夜降临，所有玩家闭眼进入夜晚阶段")
        elif req.status == STATUS_NIGHT_INFO:
            current_night = self.memory.load_variable("current_night") or 1
            self.memory.append_history(f"薄伽丘：第{current_night}夜结束，天亮了！昨夜的事件：{req.message}")
        elif req.status == STATUS_DISCUSS:  # 发言环节
            if req.name:
                # 其他玩家发言
                self.memory.append_history(f"薄伽丘：{req.name}发言：{req.message}")
            else:
                # 可信发言
                self.memory.append_history(f'薄伽丘：第{req.round}天白天开始，进入发言阶段')
                self.memory.append_history('薄伽丘：每个玩家按顺序描述自己的信息和分析')
        elif req.status == STATUS_VOTE:  # 投票环节
            self.memory.append_history(f"薄伽丘：{req.name}投票：{req.message}")
        elif req.status == STATUS_VOTE_RESULT:  # 投票环节
            out_player = req.name if req.name else req.message
            if out_player:
                self.memory.append_history(f'薄伽丘：投票结果公布，{out_player}被投票出局')
            else:
                self.memory.append_history('薄伽丘：投票结果公布，无人出局')
        elif req.status == STATUS_RESULT:
            self.memory.append_history(f"薄伽丘：游戏结束，{req.message}")
        else:
            raise NotImplementedError
        pass

    def interact(self, req=AgentReq) -> AgentResp:
        logger.info("VillagerAgent interact: {}".format(req))
        if req.status == STATUS_DISCUSS:
            if req.message:
                self.memory.append_history(req.message)
            # 使用新的模块化Prompt构建
            prompt = format_prompt(DESC_PROMPT,
                                   {"base_persona": BASE_PERSONA_PROMPT,
                                    "game_rules": GAME_RULES_SUMMARY,
                                    "villager_role": VILLAGER_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                    "action_discuss": ACTION_TEMPLATES["discuss"],
                                    "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                    "output_discuss": OUTPUT_TEMPLATES["discuss"],
                                    "name": self.memory.load_variable("name"),
                                    "history": "\n".join(self.memory.load_history())
                                    })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("VillagerAgent interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_VOTE:
            self.memory.append_history('薄伽丘：发言结束，现在进入投票阶段，每个人投票选择认为是狼人的玩家')
            choices = [name for name in req.message.split(",") if name != self.memory.load_variable("name")]  # 排除自己
            self.memory.set_variable("choices", choices)
            prompt = format_prompt(VOTE_PROMPT, {"base_persona": BASE_PERSONA_PROMPT,
                                                 "game_rules": GAME_RULES_SUMMARY,
                                                 "villager_role": VILLAGER_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                                 "action_vote": ACTION_TEMPLATES["vote"].format(choices=choices),
                                                 "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                                 "output_vote": OUTPUT_TEMPLATES["vote"],
                                                 "name": self.memory.load_variable("name"),
                                                 "choices": choices,
                                                 "history": "\n".join(self.memory.load_history())
                                                 })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("spy interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)
        else:
            raise NotImplementedError
        pass