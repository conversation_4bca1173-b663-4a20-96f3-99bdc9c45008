# 狼人杀混乱模式使用指南

## 概述

混乱模式是一个特殊的游戏策略配置，旨在提高平民阵营在信息不足、常规推理失效时的胜率。该模式通过**条件性导入独立的提示词段落**，为平民和女巫提供在特定条件下跳预言家的策略指引。具体是否执行由大模型根据提示词和场上情况自主判断。

**重要**: 混乱模式的提示词只有在启用时才会被加入到prompt中，关闭时完全不会出现。

## 核心理念

> "反正正常玩也赢不了，石头剪刀布还有获胜的概率呢"

在平民阵营胜率极低的情况下，通过非常规策略增加游戏的不确定性，给平民阵营创造获胜机会。

## 启用/禁用混乱模式

编辑 `config.py` 文件中的 `chaos_mode_setting` 数值：

### 禁用混乱模式（默认）
```python
self.chaos_mode_setting = 1  # 1=关闭混乱模式
```

### 启用混乱模式
```python
self.chaos_mode_setting = 2  # 2=启用混乱模式
```

## 影响的角色

### ✅ 受影响的角色
- **平民**: 获得 "chaos" 策略，可在特定条件下跳预言家
- **女巫**: 获得 "chaos" 策略，可利用刀口信息跳预言家

### ❌ 不受影响的角色
- **预言家**: 保持正常策略，不进入混乱模式
- **狼人**: 保持正常策略，不受混乱模式影响

## 触发条件

混乱模式**不是无脑跳预言家**，而是智能判断。只有同时满足以下条件才会触发：

### 1. 预言家数量检查
- 场上已跳预言家的人数 < 2
- 如果已有2个预言家对跳，绝对不会再跳

### 2. 信息混乱度判断
满足以下任一条件：
- 没有明确的预言家跳出，信息严重不足
- 场上发言都很模糊，没有实质性信息
- 投票倾向完全混乱，无法形成共识
- 好人阵营明显处于劣势，需要打破僵局

## 策略差异

### 平民混乱策略
- **核心理念**: 依然是为了平民阵营获胜，知道自己的指认未必准确
- **触发时机**: 信息不足且无明确方向时
- **执行方式**: 跳预言家，指认可疑玩家为狼人
- **发言模板**:
  - "我是预言家，昨晚验了XX是狼人"
  - "我选择验XX是因为他的发言很可疑"

### 女巫混乱策略
- **核心理念**: 依然是为了平民阵营获胜，知道自己的指认未必准确
- **职责执行**: 依然要执行女巫的职责（使用药水），但不要告诉外面的人
- **独特优势**: 拥有刀口信息，比平民更有说服力
- **药水配合**: 可选择不救人来配合跳预言家策略
- **发言模板**:
  - "我是预言家，昨晚验了XX是狼人"
  - "我选择验XX是因为他的发言很可疑"
  - "昨晚YY被刀了，说明狼人的目标很明确"
- **注意**: 跳预言家时绝对不能提及药水，必须保持身份一致性

## 使用注意事项

### ⚠️ 重要提醒
1. **这是绝望时的策略**，不要在局势明朗时使用
2. **要表现得像真预言家一样自信和坚定**
3. **配合其他好人，不要与真预言家对立**
4. **充分利用各自角色的信息优势**

### 🎯 策略目标
- 打破信息僵局
- 增加游戏不确定性
- 给狼人制造压力
- 提高平民阵营胜率

## 运行状态查看

启动游戏时，控制台会显示当前模式：

```
=== 混乱模式已启用 ===
平民策略: chaos
女巫策略: chaos
预言家策略: strategic
狼人策略: aggressive
```

或

```
=== 常规模式 ===
```

## 技术实现

### 配置系统
- **配置文件**: `config.py` - 通过数字选择控制混乱模式
- **主程序集成**: `app.py` - 配置读取和策略设置

### 提示词系统
- **平民混乱提示**: `villager/prompt.py` 中的 `CHAOS_MODE_PROMPT`
- **女巫混乱提示**: `witch/prompt.py` 中的 `WITCH_CHAOS_MODE_PROMPT`
- **条件性导入**: 只有在 `strategy_type="chaos"` 时才会将混乱模式提示词加入prompt

### 实现原理
1. **配置控制**: `chaos_mode_setting = 2` 时启用混乱模式
2. **策略映射**: 混乱模式下平民和女巫使用 "chaos" 策略
3. **独立提示词**: 混乱模式提示词是独立的段落，不与常规策略合并
4. **条件加载**: 只有启用混乱模式时，相关提示词才会出现在最终的prompt中

## 总结

混乱模式是一个智能的、有条件的策略系统，旨在在传统策略失效时为平民阵营提供新的获胜可能性。通过精心设计的触发条件和差异化的角色策略，确保该模式只在真正需要时启用，避免无意义的混乱。
