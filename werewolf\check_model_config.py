#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型配置检查脚本
用于验证当前的模型配置是否正确
"""

import os
import sys
from config import game_config

def check_environment_variables():
    """检查环境变量配置"""
    print("=== 环境变量检查 ===")
    
    # 检查必需的环境变量
    required_vars = ['MODEL_NAME', 'API_KEY', 'BASE_URL']
    optional_vars = ['MODEL_SERIES']
    
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✓ {var}: {value}")
        else:
            print(f"✗ {var}: 未设置")
            missing_vars.append(var)
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✓ {var}: {value}")
        else:
            print(f"- {var}: 未设置（将使用默认值）")
    
    return len(missing_vars) == 0

def check_model_series_config():
    """检查模型系列配置"""
    print("\n=== 模型系列配置检查 ===")
    
    model_series = game_config.get_model_series()
    print(f"当前模型系列: {model_series}")
    
    supported_series = list(game_config.supported_model_series.keys())
    print(f"支持的模型系列: {', '.join(supported_series)}")
    
    if model_series in supported_series:
        print("✓ 模型系列配置有效")
        return True
    else:
        print("✗ 模型系列配置无效")
        return False

def check_model_name_config():
    """检查模型名称配置"""
    print("\n=== 模型名称配置检查 ===")
    
    model_name = os.getenv('MODEL_NAME')
    if not model_name:
        print("✗ MODEL_NAME 未设置")
        return False
    
    print(f"当前模型名称: {model_name}")
    
    model_series = game_config.get_model_series()
    supported_models = game_config.get_supported_models(model_series)
    print(f"{model_series} 系列支持的模型: {', '.join(supported_models)}")
    
    if game_config.is_model_supported(model_name):
        print("✓ 模型名称在支持列表中")
        return True
    else:
        print("⚠ 模型名称不在预定义的支持列表中")
        print("  这可能是新模型或自定义模型，如果确认模型名称正确，可以继续使用")
        return True  # 允许使用不在列表中的模型

def print_configuration_examples():
    """打印配置示例"""
    print("\n=== 配置示例 ===")
    
    examples = {
        "OpenAI GPT-4": {
            "MODEL_SERIES": "openai",
            "MODEL_NAME": "gpt-4",
            "BASE_URL": "https://api.openai.com/v1"
        },
        "阿里云通义千问": {
            "MODEL_SERIES": "openai",
            "MODEL_NAME": "qwen-turbo", 
            "BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1"
        },
        "Google Gemini": {
            "MODEL_SERIES": "gemini",
            "MODEL_NAME": "gemini-1.5-pro",
            "BASE_URL": "https://generativelanguage.googleapis.com/v1"
        },
        "Anthropic Claude": {
            "MODEL_SERIES": "claude",
            "MODEL_NAME": "claude-3-5-sonnet",
            "BASE_URL": "https://api.anthropic.com"
        }
    }
    
    for name, config in examples.items():
        print(f"\n{name}:")
        for key, value in config.items():
            print(f"  {key}={value}")

def main():
    """主函数"""
    print("狼人杀游戏模型配置检查工具")
    print("=" * 50)
    
    # 检查环境变量
    env_ok = check_environment_variables()
    
    # 检查模型系列配置
    series_ok = check_model_series_config()
    
    # 检查模型名称配置
    model_ok = check_model_name_config()
    
    # 总结
    print("\n=== 检查结果总结 ===")
    if env_ok and series_ok and model_ok:
        print("✓ 所有配置检查通过，可以正常运行游戏")
        return 0
    else:
        print("⚠ 部分配置存在问题，请检查上述输出")
        print_configuration_examples()
        return 1

if __name__ == "__main__":
    sys.exit(main())
