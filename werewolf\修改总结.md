# 狼人杀游戏修改总结

## 修改概述

根据用户反馈的三个问题，对狼人杀游戏进行了以下修改：

### 问题1：平民第一夜发言问题
**问题描述**：平民说"昨晚睡得还算安稳，没有听到什么异常"，但第一夜如果女巫救了人，需要指出来。

**解决方案**：
1. 修改平民发言模板，将"昨晚睡得还算安稳"改为"我是闭眼玩家"
2. 增加平安夜分析逻辑："如果昨晚是平安夜，说明女巫可能救了人，或者狼人空刀了"
3. 在所有角色的游戏规则中添加平安夜概念

**修改文件**：
- `werewolf/villager/prompt.py` - 修改首轮发言模板
- 所有角色的prompt文件 - 添加平安夜概念到游戏规则

### 问题2：狼人悍跳策略优化
**问题描述**：狼人需要根据场上情况判断是否悍跳，如果有人已经跳了且不威胁狼人阵营可以不对跳。

**解决方案**：
1. 修改狼人悍跳策略，增加智能判断条件
2. 添加威胁评估逻辑
3. 增加抢先发声的话术："我是全场唯一真预言家"
4. 添加威胁警告："如果下一个预言家指认我是狼人，那必是悍跳狼，请大家擦亮眼睛"

**修改文件**：
- `werewolf/wolf/prompt.py` - 修改悍跳策略和发言模板

### 问题3：时间点记录混乱
**问题描述**：第三天早上还会把第一天的事情当做第二天，需要增加时间点明文辅助。

**解决方案**：
1. 在所有角色的perceive方法中增加时间点记录
2. 为夜晚阶段添加明确的轮次标记："第X夜降临"
3. 为技能使用添加时间标记："第X夜，角色使用技能"
4. 为夜晚信息添加时间标记："第X夜结束，天亮了"

**修改文件**：
- `werewolf/villager/villager_agent.py`
- `werewolf/wolf/wolf_agent.py`
- `werewolf/seer/seer_agent.py`
- `werewolf/witch/witch_agent.py`

### 问题4：预言家自证话术增强
**问题描述**：需要为预言家增加更多的自证话术。

**解决方案**：
1. 增加预言家自证核心要点
2. 添加验人逻辑自证话术
3. 增加反击悍跳的专业表述
4. 添加求神配合和团结呼吁的话术

**修改文件**：
- `werewolf/seer/prompt.py` - 增加自证话术和策略

## 具体修改内容

### 1. 平民发言模板修改

**修改前**：
```
- 夜晚感受: "昨晚睡得还算安稳，没有听到什么异常"
```

**修改后**：
```
- 身份表明: "我是闭眼玩家，昨晚没有任何特殊信息"
- 平安夜分析: "如果昨晚是平安夜，说明女巫可能救了人，或者狼人空刀了"
- 死人情况分析: "如果昨晚有人死亡，我们需要分析狼人的刀法意图"
```

### 2. 狼人悍跳策略修改

**增加悍跳判断条件**：
```
**悍跳判断条件** (必须满足以下条件才悍跳):
* **预言家威胁评估**: 如果已有人跳预言家，评估其是否对狼人阵营构成真正威胁
* **场上局势判断**: 如果没有人跳预言家，必须抢先悍跳占据主导权
* **队友配合**: 确保队友能够配合自己的悍跳策略
```

**增加威胁警告话术**：
```
- 抢先悍跳: 如果没人跳预言家，立即悍跳："我是全场唯一真预言家"
- 威胁警告: "如果下一个预言家指认我是狼人，那必是悍跳狼，请大家擦亮眼睛"
```

### 3. 时间点记录优化

**所有角色增加时间记录**：
```python
elif req.status == STATUS_NIGHT:
    # 记录当前夜晚轮次
    current_round = getattr(req, 'round', 1)
    self.memory.set_variable("current_night", current_round)
    self.memory.append_history(f"薄伽丘：第{current_round}夜降临，所有玩家闭眼进入夜晚阶段")
```

### 4. 预言家自证话术增强

**增加自证核心要点**：
```
* **验人逻辑自证**: "我的验人顺序完全符合预言家思路，先验可疑位置"
* **身份坚持**: "我敢以预言家身份担保，这就是我的真实验人结果"
* **逻辑对比**: "真预言家的验人都有充分理由，悍跳狼往往逻辑混乱"
* **持续自证**: "我的每一个验人都经得起推敲，这就是真预言家的表现"
```

## 预期效果

1. **平民发言更合理**：不再说"睡得安稳"，改为专业的"闭眼玩家"表述，并能正确分析平安夜情况
2. **狼人悍跳更智能**：能够根据场上情况判断是否需要悍跳，避免无意义的对跳
3. **时间记录更清晰**：每个事件都有明确的时间标记，避免混淆不同轮次的信息
4. **预言家自证更强**：拥有更多专业的自证话术，能够更好地证明自己的身份

## 注意事项

1. 所有角色都了解平安夜概念，这是游戏的基础知识
2. 狼人的悍跳策略更加智能，但仍保持激进的基本策略
3. 时间记录的改进需要确保所有角色都能正确理解当前游戏进度
4. 预言家的自证话术需要在实际游戏中验证效果
