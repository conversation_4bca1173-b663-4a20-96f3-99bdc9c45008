# 狼人杀游戏修改总结

## 修改概述

根据用户反馈的三个问题，对狼人杀游戏进行了以下修改：

### 问题1：平民第一夜发言问题
**问题描述**：平民说"昨晚睡得还算安稳，没有听到什么异常"，但第一夜如果女巫救了人，需要指出来。

**解决方案**：
1. 修改平民发言模板，将"昨晚睡得还算安稳"改为"我是闭眼玩家"
2. 增加平安夜分析逻辑："如果昨晚是平安夜，说明女巫可能救了人，或者狼人空刀了"
3. 在所有角色的游戏规则中添加平安夜概念

**修改文件**：
- `werewolf/villager/prompt.py` - 修改首轮发言模板
- 所有角色的prompt文件 - 添加平安夜概念到游戏规则

### 问题2：狼人悍跳策略优化
**问题描述**：狼人需要根据场上情况判断是否悍跳，如果有人已经跳了且不威胁狼人阵营可以不对跳。

**解决方案**：
1. 修改狼人悍跳策略，增加智能判断条件
2. 添加威胁评估逻辑
3. 增加抢先发声的话术："我是全场唯一真预言家"
4. 添加威胁警告："如果下一个预言家指认我是狼人，那必是悍跳狼，请大家擦亮眼睛"

**修改文件**：
- `werewolf/wolf/prompt.py` - 修改悍跳策略和发言模板

### 问题3：时间点记录混乱
**问题描述**：第三天早上还会把第一天的事情当做第二天，需要增加时间点明文辅助。

**解决方案**：
1. 在所有角色的perceive方法中增加时间点记录
2. 为夜晚阶段添加明确的轮次标记："第X夜降临"
3. 为技能使用添加时间标记："第X夜，角色使用技能"
4. 为夜晚信息添加时间标记："第X夜结束，天亮了"

**修改文件**：
- `werewolf/villager/villager_agent.py`
- `werewolf/wolf/wolf_agent.py`
- `werewolf/seer/seer_agent.py`
- `werewolf/witch/witch_agent.py`

### 问题4：预言家自证话术增强
**问题描述**：需要为预言家增加更多的自证话术。

**解决方案**：
1. 增加预言家自证核心要点
2. 添加验人逻辑自证话术
3. 增加反击悍跳的专业表述
4. 添加求神配合和团结呼吁的话术

**修改文件**：
- `werewolf/seer/prompt.py` - 增加自证话术和策略

## 具体修改内容

### 1. 平民发言模板修改

**修改前**：
```
- 夜晚感受: "昨晚睡得还算安稳，没有听到什么异常"
```

**修改后**：
```
- 身份表明: "我是闭眼玩家，昨晚没有任何特殊信息"
- 平安夜分析: "如果昨晚是平安夜，说明女巫可能救了人，或者狼人空刀了"
- 死人情况分析: "如果昨晚有人死亡，我们需要分析狼人的刀法意图"
```

### 2. 狼人悍跳策略修改

**增加悍跳判断条件**：
```
**悍跳判断条件** (必须满足以下条件才悍跳):
* **预言家威胁评估**: 如果已有人跳预言家，评估其是否对狼人阵营构成真正威胁
* **场上局势判断**: 如果没有人跳预言家，必须抢先悍跳占据主导权
* **队友配合**: 确保队友能够配合自己的悍跳策略
```

**增加威胁警告话术**：
```
- 抢先悍跳: 如果没人跳预言家，立即悍跳："我是全场唯一真预言家"
- 威胁警告: "如果下一个预言家指认我是狼人，那必是悍跳狼，请大家擦亮眼睛"
```

### 3. 时间点记录优化

**所有角色增加时间记录**：
```python
elif req.status == STATUS_NIGHT:
    # 记录当前夜晚轮次
    current_round = getattr(req, 'round', 1)
    self.memory.set_variable("current_night", current_round)
    self.memory.append_history(f"薄伽丘：第{current_round}夜降临，所有玩家闭眼进入夜晚阶段")
```

### 4. 预言家自证话术增强

**增加自证核心要点**：
```
* **验人逻辑自证**: "我的验人顺序完全符合预言家思路，先验可疑位置"
* **身份坚持**: "我敢以预言家身份担保，这就是我的真实验人结果"
* **逻辑对比**: "真预言家的验人都有充分理由，悍跳狼往往逻辑混乱"
* **持续自证**: "我的每一个验人都经得起推敲，这就是真预言家的表现"
```

## 新增修改（基于badcase反馈）

### 问题5：预言家缺少预防针话术
**问题描述**：预言家需要为狼人的反驳和误导打预防针，强调自己跳出来后的生存危险。

**解决方案**：
1. 增加生存警告话术："我作为真预言家跳出来，晚上肯定活不成了，请大家擦亮眼睛"
2. 添加预防针话术："XX被查杀后肯定会反驳和带节奏，请大家不要被误导"
3. 增加团结呼吁："只要有一个真平民被带跑偏了，本回合就投不出去狼人，不要让狼人得逞"

### 问题6：狼人混淆视听能力不足
**问题描述**：狼人需要加强混淆视听的能力，更好地质疑预言家。

**解决方案**：
1. 增加平安夜混淆话术："昨晚平安夜说明他可能在混淆视听"
2. 添加质疑动机话术："预言家通常不会主动点明查杀对象，XX这么急着带节奏很可疑"
3. 增加转移焦点策略："我觉得更可疑的是XX，建议大家多关注"

### 问题7：投票策略和分析不足
**问题描述**：需要明确投票指导原则，并重视投票情况分析。

**解决方案**：
1. **好人阵营投票原则**：
   - 单预言家时优先相信预言家
   - 双预言家时判断真假
   - 从第二回合起重点分析投票情况
2. **狼人投票策略**：
   - 可以力挺队友，也可以隐狼深潜
   - 必要时可以卖掉队友营造好人形象
3. **投票分析重点**：
   - 分析配合关系和立场变化
   - 识别可疑的投票行为
   - 根据投票情况推断狼坑

## 具体新增内容

### 1. 预言家预防针话术
```
- 生存警告: "我作为真预言家跳出来，晚上肯定活不成了，请大家擦亮眼睛"
- 预防针话术: "XX被查杀后肯定会反驳和带节奏，请大家不要被误导"
- 团结呼吁: "只要有一个真平民被带跑偏了，本回合就投不出去狼人，不要让狼人得逞"
```

### 2. 狼人混淆视听话术
```
- 平安夜混淆: "昨晚平安夜说明他可能在混淆视听，真预言家不会这样急着带节奏"
- 质疑动机: "预言家通常不会主动点明查杀对象，XX这么急着带节奏很可疑"
- 转移焦点: "我觉得更可疑的是XX，他的发言逻辑有问题，建议大家多关注"
```

### 3. 投票策略优化
```
**好人阵营**:
- 单预言家策略: "如果只有一个预言家跳出来，优先相信预言家的查杀结果"
- 双预言家策略: "如果有两个预言家对跳，需要判断谁是真预言家，谁是悍跳狼"
- 投票情况分析: "从第二回合起，要重点分析投票情况，看哪些人可能是狼人"

**狼人阵营**:
- 灵活应变策略: "可以选择力挺队友，也可以作为隐狼潜伏在人类中，必要时卖掉队友"
- 隐狼深潜策略: "作为隐狼时，可以适当质疑队友，营造好人形象，关键时刻再配合"
```

## 预期效果

1. **平民发言更合理**：不再说"睡得安稳"，改为专业的"闭眼玩家"表述，并能正确分析平安夜情况
2. **狼人悍跳更智能**：能够根据场上情况判断是否需要悍跳，避免无意义的对跳
3. **时间记录更清晰**：每个事件都有明确的时间标记，避免混淆不同轮次的信息
4. **预言家自证更强**：拥有更多专业的自证话术，能够更好地证明自己的身份
5. **预言家预防针更有效**：能够提前为狼人的反驳做准备，减少好人被误导的可能
6. **狼人混淆视听更强**：拥有更多质疑预言家的话术，增强误导能力
7. **投票策略更明确**：所有角色都有清晰的投票指导原则和分析方法

## 注意事项

1. 所有角色都了解平安夜概念，这是游戏的基础知识
2. 狼人的悍跳策略更加智能，但仍保持激进的基本策略
3. 时间记录的改进需要确保所有角色都能正确理解当前游戏进度
4. 预言家的自证话术和预防针需要在实际游戏中验证效果
5. 投票分析从第二回合起成为重要的判断依据
6. 狼人的投票策略更加灵活，可以根据局势选择不同的配合方式
