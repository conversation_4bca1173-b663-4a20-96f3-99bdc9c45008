import os

from agent_build_sdk.builder import <PERSON><PERSON><PERSON><PERSON>
from seer.seer_agent import Seer<PERSON>gent
from villager.villager_agent import Villager<PERSON>gent
from witch.witch_agent import Witch<PERSON><PERSON>
from wolf.wolf_agent import WolfAgent
from agent_build_sdk.model.roles import ROLE_VILLAGER,ROLE_WOLF,R<PERSON><PERSON>_SEER,ROLE_WITCH,ROL<PERSON>_HUNTER
from agent_build_sdk.sdk.werewolf_agent import WerewolfAgent
from config import game_config

if __name__ == '__main__':
    name = 'spy'
    agent = WerewolfAgent(name, model_name=os.getenv('MODEL_NAME'))

    # 创建角色Agent实例
    villager_agent = VillagerAgent(model_name=os.getenv('MODEL_NAME'))
    wolf_agent = WolfAgent(model_name=os.getenv('MODEL_NAME'))
    seer_agent = SeerAgent(model_name=os.getenv('MODEL_NAME'))
    witch_agent = WitchAgent(model_name=os.getenv('MODEL_NAME'))

    # 根据配置设置策略
    villager_agent.set_strategy(game_config.get_strategy("villager"))
    wolf_agent.set_strategy(game_config.get_strategy("wolf"))
    seer_agent.set_strategy(game_config.get_strategy("seer"))
    witch_agent.set_strategy(game_config.get_strategy("witch"))

    # 注册角色Agent
    agent.register_role_agent(ROLE_VILLAGER, villager_agent)
    agent.register_role_agent(ROLE_WOLF, wolf_agent)
    agent.register_role_agent(ROLE_SEER, seer_agent)
    agent.register_role_agent(ROLE_WITCH, witch_agent)

    # 输出当前配置状态
    if game_config.is_chaos_mode():
        print("=== 混乱模式已启用 ===")
        print("平民策略:", game_config.get_strategy("villager"))
        print("女巫策略:", game_config.get_strategy("witch"))
        print("预言家策略:", game_config.get_strategy("seer"))
        print("狼人策略:", game_config.get_strategy("wolf"))
    else:
        print("=== 常规模式 ===")

    agent_builder = AgentBuilder(name, agent=agent)
    agent_builder.start()