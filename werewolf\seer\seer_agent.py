from agent_build_sdk.model.roles import ROLE_SEER
from agent_build_sdk.model.werewolf_model import <PERSON><PERSON><PERSON><PERSON>, AgentReq, STATUS_START, STATUS_WOLF_SPEECH, \
    STATUS_VOTE_RESULT, STATUS_SKILL, STATUS_SKILL_RESULT, STATUS_NIGHT_INFO, STATUS_DAY, STATUS_DISCUSS, STATUS_VOTE, \
    STATUS_RESULT, STATUS_NIGHT, STATUS_SKILL
from agent_build_sdk.utils.logger import logger
from agent_build_sdk.sdk.role_agent import BasicRoleAgent
from common.safe_format import format_prompt
from seer.prompt import (
    DESC_PROMPT, VOTE_PROMPT, SKILL_PROMPT,
    BASE_PERSONA_PROMPT, GAME_RULES_SUMMARY, SEER_ROLE_PROMPT,
    ACTION_TEMPLATES, STRATEGY_TEMPLATES, OUTPUT_TEMPLATES,
    build_prompt
)


class SeerAgent(BasicRoleAgent):
    """预言家角色Agent"""

    def __init__(self, model_name):
        super().__init__(ROLE_SEER, model_name=model_name)
        self.memory.set_variable("checked_players", {})  # 存储已查验的玩家信息
        # 默认策略类型，可以根据需要动态调整
        self.strategy_type = "strategic"  # 可选: "conservative", "aggressive", "strategic", "lowkey"

    def set_strategy(self, strategy_type):
        """设置预言家的策略类型"""
        if strategy_type in ["conservative", "aggressive", "strategic", "lowkey"]:
            self.strategy_type = strategy_type
        else:
            logger.warning(f"未知的策略类型: {strategy_type}, 使用默认策略 'strategic'")
            self.strategy_type = "strategic"

    def perceive(self, req=AgentReq):
        if req.status == STATUS_START:
            self.memory.clear()
            self.memory.set_variable("name", req.name)
            self.memory.set_variable("checked_players", {})  # 重置已查验的玩家信息
            self.memory.append_history("薄伽丘：游戏开始，这是一场6人局狼人杀游戏，包括预言家、女巫、猎人、平民和狼人")
            self.memory.append_history(f"薄伽丘：{req.name}被分配到的角色是[预言家]")
        elif req.status == STATUS_NIGHT:
            self.memory.append_history("薄伽丘：夜晚降临，所有玩家闭眼进入夜晚阶段")
        elif req.status == STATUS_SKILL_RESULT:
            # 记录查验结果，使用薄伽丘叙述
            if req.name and req.message:
                # 解析查验结果
                if "狼人" in req.message or "wolf" in req.message.lower():
                    result_desc = "狼人"
                elif "好人" in req.message or "villager" in req.message.lower() or "平民" in req.message:
                    result_desc = "好人"
                else:
                    result_desc = req.message

                self.memory.append_history(f"薄伽丘：预言家{self.memory.load_variable('name')}查验了{req.name}，结果显示{req.name}是{result_desc}")

                # 更新已查验玩家信息
                checked_players = self.memory.load_variable("checked_players")
                checked_players[req.name] = result_desc
                self.memory.set_variable("checked_players", checked_players)
        elif req.status == STATUS_NIGHT_INFO:
            self.memory.append_history(f"薄伽丘：天亮了！昨夜的事件：{req.message}")
        elif req.status == STATUS_DISCUSS:  # 发言环节
            if req.name:
                # 其他玩家发言
                self.memory.append_history(f"薄伽丘：{req.name}发言：{req.message}")
            else:
                # 可信发言
                self.memory.append_history(f'薄伽丘：第{req.round}天白天开始，进入发言阶段')
                self.memory.append_history('薄伽丘：每个玩家按顺序描述自己的信息和分析')
        elif req.status == STATUS_VOTE:  # 投票环节
            self.memory.append_history(f"薄伽丘：{req.name}投票：{req.message}")
        elif req.status == STATUS_VOTE_RESULT:  # 投票环节
            out_player = req.name if req.name else req.message
            if out_player:
                self.memory.append_history(f'薄伽丘：投票结果公布，{out_player}被投票出局')
            else:
                self.memory.append_history('薄伽丘：投票结果公布，无人出局')
        elif req.status == STATUS_RESULT:
            self.memory.append_history(f"薄伽丘：游戏结束，{req.message}")
        else:
            raise NotImplementedError

    def interact(self, req=AgentReq) -> AgentResp:
        logger.info("seer interact: {}".format(req))
        if req.status == STATUS_DISCUSS:
            if req.message:
                self.memory.append_history(req.message)
            checked_players = self.memory.load_variable("checked_players")
            # 使用新的模块化Prompt构建
            prompt = format_prompt(DESC_PROMPT,
                                  {"base_persona": BASE_PERSONA_PROMPT,
                                   "game_rules": GAME_RULES_SUMMARY,
                                   "seer_role": SEER_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                   "action_discuss": ACTION_TEMPLATES["discuss"],
                                   "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                   "output_discuss": OUTPUT_TEMPLATES["discuss"],
                                   "name": self.memory.load_variable("name"),
                                   "checked_players": checked_players,
                                   "history": "\n".join(self.memory.load_history())
                                  })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("seer interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_VOTE:
            self.memory.append_history('薄伽丘：发言结束，现在进入投票阶段，每个人投票选择认为是狼人的玩家')
            checked_players = self.memory.load_variable("checked_players")
            choices = [name for name in req.message.split(",") if name != self.memory.load_variable("name")]  # 排除自己
            self.memory.set_variable("choices", choices)
            prompt = format_prompt(VOTE_PROMPT, {"base_persona": BASE_PERSONA_PROMPT,
                                               "game_rules": GAME_RULES_SUMMARY,
                                               "seer_role": SEER_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                               "action_vote": ACTION_TEMPLATES["vote"].format(choices=choices),
                                               "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                               "output_vote": OUTPUT_TEMPLATES["vote"],
                                               "name": self.memory.load_variable("name"),
                                               "checked_players": checked_players,
                                               "choices": choices,
                                               "history": "\n".join(self.memory.load_history())
                                              })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("seer interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_SKILL:
            checked_players = self.memory.load_variable("checked_players")
            choices = [name for name in req.message.split(",")
                      if name != self.memory.load_variable("name") and name not in checked_players]  # 排除自己和已查验的
            self.memory.set_variable("choices", choices)
            prompt = format_prompt(SKILL_PROMPT, {
                "base_persona": BASE_PERSONA_PROMPT,
                "game_rules": GAME_RULES_SUMMARY,
                "seer_role": SEER_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                "action_skill": ACTION_TEMPLATES["skill"].format(choices=choices),
                "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                "output_skill": OUTPUT_TEMPLATES["skill"],
                "name": self.memory.load_variable("name"),
                "checked_players": checked_players,
                "choices": choices,
                "history": "\n".join(self.memory.load_history())
            })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("seer skill result: {}".format(result))
            return AgentResp(success=True, result=result, skillTargetPlayer=result, errMsg=None)
        else:
            raise NotImplementedError