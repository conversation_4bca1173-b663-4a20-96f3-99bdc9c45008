# 狼人杀游戏模型系列支持说明

## 概述

本项目现已支持多种大语言模型系列，包括OpenAI兼容API、Google Gemini和Anthropic Claude系列。用户可以通过环境变量轻松切换不同的模型系列。

## 支持的模型系列

### 1. OpenAI系列（兼容OpenAI API）

**特点**：支持所有兼容OpenAI API格式的模型
**配置**：
```bash
MODEL_SERIES=openai
MODEL_NAME=gpt-4
API_KEY=sk-your-openai-api-key
BASE_URL=https://api.openai.com/v1
```

**支持的模型**：
- **OpenAI官方模型**：
  - gpt-4
  - gpt-4-turbo
  - gpt-3.5-turbo
  - gpt-4o
  - gpt-4o-mini

- **阿里云通义千问**：
  ```bash
  MODEL_SERIES=openai
  MODEL_NAME=qwen-turbo
  API_KEY=your-dashscope-api-key
  BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
  ```

- **其他第三方提供商**：任何兼容OpenAI API格式的模型

### 2. Google Gemini系列

**配置**：
```bash
MODEL_SERIES=gemini
MODEL_NAME=gemini-1.5-pro
API_KEY=your-gemini-api-key
BASE_URL=https://generativelanguage.googleapis.com/v1
```

**支持的模型**：
- gemini-pro
- gemini-1.5-pro
- gemini-1.5-flash
- gemini-ultra

### 3. Anthropic Claude系列

**配置**：
```bash
MODEL_SERIES=claude
MODEL_NAME=claude-3-5-sonnet
API_KEY=your-claude-api-key
BASE_URL=https://api.anthropic.com
```

**支持的模型**：
- claude-3-opus
- claude-3-sonnet
- claude-3-haiku
- claude-3-5-sonnet

## 配置方法

### 1. 环境变量配置

创建 `.env` 文件或设置环境变量：

```bash
# 必需的环境变量
MODEL_SERIES=openai          # 模型系列
MODEL_NAME=gpt-4            # 具体模型名称
API_KEY=your-api-key        # API密钥
BASE_URL=https://api.openai.com/v1  # API基础URL

# 可选的环境变量
# 其他配置...
```

### 2. 使用配置示例文件

1. 复制示例文件：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，填入真实的配置信息

### 3. 验证配置

使用配置检查工具验证设置：
```bash
python check_model_config.py
```

## 配置验证功能

### 自动验证
- 启动时自动检查模型配置
- 显示当前配置信息
- 验证模型系列和模型名称的匹配性

### 手动检查工具
运行 `check_model_config.py` 可以：
- 检查所有环境变量是否正确设置
- 验证模型系列配置
- 确认模型名称是否在支持列表中
- 提供详细的配置示例

### 输出示例
```
=== 模型配置信息 ===
模型系列: openai
模型名称: gpt-4
支持的模型: gpt-4, gpt-4-turbo, gpt-3.5-turbo, gpt-4o, gpt-4o-mini
✓ 模型配置验证通过

=== 游戏策略配置 ===
混乱模式: 关闭（常规模式）
平民策略: rational
女巫策略: strategic
预言家策略: strategic
狼人策略: aggressive
```

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要提交到版本控制系统
2. **模型匹配**：确保MODEL_SERIES与MODEL_NAME匹配
3. **BASE_URL设置**：不同模型系列需要不同的BASE_URL
4. **API格式差异**：虽然支持多种模型，但底层仍依赖agent_build_sdk的实现
5. **新模型支持**：如果使用不在预定义列表中的新模型，系统会给出警告但仍可继续运行

## 扩展支持

如需添加新的模型系列或模型，可以修改 `config.py` 中的 `supported_model_series` 字典：

```python
self.supported_model_series = {
    "openai": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"],
    "gemini": ["gemini-pro", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-ultra"],
    "claude": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-3-5-sonnet"],
    # 添加新的模型系列
    "new_series": ["model1", "model2", "model3"]
}
```

## 故障排除

### 常见问题

1. **模型系列不支持**
   - 检查MODEL_SERIES是否为：openai、gemini、claude之一
   - 确认拼写正确（全小写）

2. **模型名称警告**
   - 如果模型名称不在预定义列表中，会显示警告但仍可运行
   - 确认模型名称与API提供商的文档一致

3. **API连接失败**
   - 检查BASE_URL是否正确
   - 确认API_KEY有效
   - 验证网络连接

### 获取帮助

如果遇到配置问题，可以：
1. 运行 `python check_model_config.py` 进行诊断
2. 查看 `.env.example` 文件中的配置示例
3. 检查对应模型提供商的API文档
