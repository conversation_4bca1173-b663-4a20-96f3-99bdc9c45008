# ===== 狼人杀游戏环境变量配置示例 =====

# 模型系列配置
# 支持的模型系列：openai, gemini, claude
MODEL_SERIES=openai

# 模型名称配置
# 根据选择的模型系列，设置对应的模型名称

# OpenAI系列（兼容OpenAI API的模型）
# MODEL_NAME=gpt-4
# MODEL_NAME=gpt-4-turbo
# MODEL_NAME=gpt-3.5-turbo
# MODEL_NAME=gpt-4o
# MODEL_NAME=gpt-4o-mini

# Gemini系列
# MODEL_NAME=gemini-pro
# MODEL_NAME=gemini-1.5-pro
# MODEL_NAME=gemini-1.5-flash
# MODEL_NAME=gemini-ultra

# Claude系列
# MODEL_NAME=claude-3-opus
# MODEL_NAME=claude-3-sonnet
# MODEL_NAME=claude-3-haiku
# MODEL_NAME=claude-3-5-sonnet

# 默认使用OpenAI系列的gpt-4
MODEL_NAME=gpt-4

# API配置
# API密钥（根据使用的模型系列设置对应的API密钥）
API_KEY=your_api_key_here

# API基础URL
# OpenAI官方API
# BASE_URL=https://api.openai.com/v1

# 阿里云兼容OpenAI API
# BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 其他兼容OpenAI API的服务商
# BASE_URL=https://your-provider.com/v1

BASE_URL=https://api.openai.com/v1

# ===== 使用示例 =====

# 示例1：使用OpenAI GPT-4
# MODEL_SERIES=openai
# MODEL_NAME=gpt-4
# API_KEY=sk-your-openai-api-key
# BASE_URL=https://api.openai.com/v1

# 示例2：使用Gemini Pro
# MODEL_SERIES=gemini
# MODEL_NAME=gemini-1.5-pro
# API_KEY=your-gemini-api-key
# BASE_URL=https://generativelanguage.googleapis.com/v1

# 示例3：使用Claude 3.5 Sonnet
# MODEL_SERIES=claude
# MODEL_NAME=claude-3-5-sonnet
# API_KEY=your-claude-api-key
# BASE_URL=https://api.anthropic.com

# 示例4：使用阿里云兼容OpenAI API
# MODEL_SERIES=openai
# MODEL_NAME=qwen-turbo
# API_KEY=your-dashscope-api-key
# BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# ===== 注意事项 =====
# 1. 将此文件复制为 .env 并填入真实的API密钥
# 2. 确保选择的模型系列与模型名称匹配
# 3. 不同的模型系列可能需要不同的BASE_URL
# 4. 请妥善保管API密钥，不要提交到版本控制系统
