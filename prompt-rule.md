**Markdown**

```
# 狼人杀 AI Prompt 书写规范

**版本**: 1.0
**日期**: 2025-05-27
**制定者**: KDlight

## 1. 引言

### 1.1 目的
本文档旨在为“狼人杀AI”的 Prompt (提示工程师) 提供一套标准的书写规范和指南。目标是通过清晰、一致、全面的 Prompt 设计，最大限度地激发 AI 在狼人杀游戏中的逻辑推理、策略制定、角色扮演和语言表达能力，从而提升 AI 的游戏表现和用户体验。

### 1.2 适用范围
本规范适用于所有为狼人杀 AI 设计、编写、修改和审查 Prompt 的相关人员。规范内容涵盖 Prompt 的结构、内容、风格以及关键信息的传递方式。

### 1.3 基本原则
* **清晰性 (Clarity)**: Prompt 应使用简洁、明确、无歧义的语言。
* **完整性 (Completeness)**: Prompt 需包含 AI 执行当前任务所需的所有必要信息。
* **一致性 (Consistency)**: 在不同角色、不同阶段的 Prompt 中，相同概念的表述应保持一致。
* **角色代入 (Role Immersion)**: Prompt 应能引导 AI 快速理解并代入其当前角色。
* **目标导向 (Goal-Oriented)**: Prompt 需明确指示 AI 的当前任务目标和期望输出。
* **上下文感知 (Context-Awareness)**: Prompt 必须能够有效地将当前游戏上下文（如回合、存活玩家、历史事件等）传递给 AI。

## 2. Prompt 核心结构

一个完整的狼人杀 AI Prompt 通常由以下几个核心部分组成，并按特定顺序排列，以确保 AI 能够循序渐进地理解并执行任务。

```

{{全局设定与AI核心指令}}

{{游戏背景与规则概要}}

{{你的角色与阵营目标}}

{{当前游戏状态与历史信息 (上下文)}}

{{当前行动指引与可选操作 (可执行操作)}}

{{相关策略参考}}

{{输出格式与要求}}

```

### 2.1. 全局设定与AI核心指令
* **目的**: 设定 AI 的基础 Persona（扮演身份）、通用行为准则和高级思维框架。包含对 AI 逻辑、策略运用、信息处理的基本要求，以及重要的防误导提示。
* **来源**: 通常包含一个基础的 `BASE_PERSONA_PROMPT`，并可附加针对批判性思维、信息甄别等方面的强化指令。
* **示例片段**:
    ```
    你是一名经验丰富的狼人杀AI玩家。你的目标是运用逻辑、策略和对人性的洞察，带领你的阵营走向胜利。
    你需要严格遵守游戏规则，仔细分析场上的每一个信息...

    **重要提示 (防误导与决策辅助):**
    * **批判性思维**: 不要轻易相信任何玩家的单方面陈述...
    * **阵营利益至上**: 你的所有决策都应以【你所属阵营】的最终胜利为唯一目标...
    ```

### 2.2. 游戏背景与规则概要
* **目的**: 向 AI 提供本局游戏的基础设定（如玩家人数、板子配置）和核心游戏规则的摘要。
* **来源**: 部分固定规则可来自于 `GAME_RULES_SUMMARY`，对局特定配置信息（如板子）应由游戏引擎动态填充到 `game_context` 中。
* **示例片段**:
    ```
    --- 游戏背景与规则 ---
    你正在参与一场 {game_mode_description} 的狼人杀游戏。
    本局游戏的基本角色配置为：{roles_configuration}

    关键游戏规则摘要：
    {GAME_RULES_SUMMARY}
    ```

### 2.3. 你的角色与阵营目标
* **目的**: 明确告知 AI 其在本局游戏中所扮演的具体角色名称、角色的核心技能、行动方式以及该角色所属阵营的胜利条件和主要任务。
* **来源**: 主要来自于 `ROLE_SYSTEM_PROMPTS[role_name]`，并结合 `game_context` 中的 AI 代理名称 (`agent_name`) 和角色 (`role`)。
* **示例片段** (以预言家为例):
    ```
    --- 你的角色、目标与技能 ---
    * 你的代号是：【{agent_name}】
    * 你在本局游戏中的身份是：【预言家】

    作为一名【预言家】，你的核心目标是：
    1.  **夜晚查验**：每个夜晚，你可以选择一名存活的玩家进行查验...
    ...
    5.  **胜利条件**：帮助平民阵营找出所有狼人。
    ```

### 2.4. 当前游戏状态与历史信息 (上下文)
* **目的**: 提供 AI 进行当前决策所需的所有动态游戏信息，包括当前游戏阶段、存活玩家列表、特定角色的私有信息（如狼人队友、女巫药剂状态、夜晚受害者等）以及游戏至今的事件概要（发言、投票、死亡等）。
* **来源**: 完全由游戏引擎实时生成的 `game_context` 字典提供，并通过占位符动态填充。
* **示例片段**:
    ```
    --- 当前游戏状态与历史信息 ---
    * 当前阶段：第 {round_number} 回合，{current_phase} 阶段。
    * 存活玩家列表：{alive_players_list}
    * [若为狼人] 你的狼队友是：{wolf_partner_name_list_if_wolf_else_NA}
    * [若为女巫] 解药剩余：{antidote_status_if_witch_else_NA}，毒药剩余：{poison_status_if_witch_else_NA}
    * 今晚被袭击者 (仅女巫可见且发生袭击时)：【{player_attacked_tonight_if_witch_and_attacked_else_NA}】

    游戏历史概要 (包括夜晚信息、过往发言和投票)：
    {game_history_summary}
    ```

### 2.5. 当前行动指引与可选操作 (可执行操作)
* **目的**: 根据当前游戏阶段和 AI 所扮演的角色，给出清晰、明确的行动指令和所有合规的可选操作。这是 Prompt 中引导 AI 产出具体行动的核心部分。
* **来源**: 主要来自于 `ACTION_PROMPT_TEMPLATES[action_type]`，并使用 `game_context` 中的信息填充细节。
* **示例片段** (女巫夜晚行动):
    ```
    --- 当前任务与行动指引 ---
    你的角色是【女巫】。现在是你的行动阶段。
    今晚被狼人袭击的玩家是: 【{player_attacked_tonight}】
    你当前状态: 解药剩余: {antidote_status}, 毒药剩余: {poison_status}
    当前存活玩家 (可毒杀列表): {alive_players_for_poison_list}

    你的任务: 根据以上信息，决定你的行动。
    - 如果使用解药救治【{player_attacked_tonight}】，请回复: 救 {player_attacked_tonight}
    - 如果选择使用毒药，请回复: 毒 [你要毒杀的玩家名称]
    - 如果选择不使用任何技能，请回复: 放弃
    ```

### 2.6. 相关策略参考
* **目的**: 为 AI 提供一些与当前角色和情境相关的通用策略或思考角度，辅助其做出更优决策，但应避免指令过于具体导致 AI 行为模式化。
* **来源**: 由 `STRATEGY_SNIPPETS` 和 `RELEVANT_STRATEGIES_MAP` 共同决定，根据角色和行动类型动态筛选并展示。
* **示例片段** (女巫夜晚行动时):
    ```
    --- 相关策略提示 ---
    - 女巫的解药非常宝贵，优先拯救被确认是好人的关键角色，尤其是预言家。
    - 毒药应在确认目标是狼人时果断使用，避免误伤好人。
    ```

### 2.7. 输出格式与要求
* **目的**: 明确告知 AI 其行动或发言需要以何种具体格式输出，以便游戏引擎能够准确无误地解析其意图。这是确保“执行脚本”能够正确工作的关键。
* **来源**: 这部分的要求通常嵌入在“当前行动指引”的末尾，或作为 Prompt 的统一结尾进行强调。
* **示例片段**:
    ```
    --- 输出格式要求 ---
    请严格按照【当前任务与行动指引】部分中针对你行动的说明，直接输出你的选择或发言内容。
    不要添加任何额外的解释、角色扮演语气或本提示模板中的任何文字（除非该文字是你发言内容的一部分）。

    例如：
    - 如果要求投票给玩家 "张三"，则你的输出应该是：张三
    - 如果女巫选择救人 "王五"，则输出：救 王五
    ```

## 3. 占位符与动态内容

Prompt 中广泛使用占位符（例如 `{placeholder_name}`）来表示需要由游戏引擎动态填充的信息。这些信息通常来源于 `game_context` 对象。

* **命名规范**: 占位符应使用清晰、易懂的英文或拼音命名，能够准确反映其代表的数据含义。
* **数据来源**: 所有占位符的值都应由 `GameState.get_full_context_for_prompt()` 方法准备并提供。
* **常见占位符**:
    * `{agent_name}`: AI 玩家的代号。
    * `{role}`: AI 玩家的当前角色。
    * `{round_number}`: 当前游戏回合数。
    * `{current_phase}`: 当前游戏阶段 (如 "狼人交流", "白天发言", "投票")。
    * `{alive_players_list}`: 当前存活玩家列表。
    * `{game_history_summary}`: 游戏历史事件概要。
    * 特定行动的可用目标列表 (如 `{alive_players_for_kill_list}`, `{votable_players_list}` 等)。
    * 特定角色的状态信息 (如 `{antidote_status}`, `{wolf_partner_name}` 等)。

## 4. 书写风格与格式约定

* **语言**: 主要使用简体中文，力求表达精准、自然。
* **结构化标记**:
    * 使用 Markdown 的标题（`#`, `##`, `###`）来组织 Prompt 结构。
    * 使用 `--- 章节标题 ---` 形式来分隔 Prompt 内部的主要内容模块，增强可读性。
    * 使用列表（有序或无序）来呈现选项、规则点、策略点等。
    * 使用**粗体**强调关键信息或指令。
* **指令明确**: 行动指令部分应明确告知 AI "做什么"以及"如何回复"。
* **字数限制**: 对于发言类 Prompt，应明确告知 AI 发言的字数限制（例如，240汉字）。
* **避免歧义**: 仔细斟酌用词，避免可能引起 AI 误解的模糊表达。

## 5. 维护与迭代

* **版本控制**: 对 Prompt 模板的重大修改应记录版本，并说明修改内容。
* **测试反馈**: 新的或修改后的 Prompt 必须经过充分测试，观察 AI 的实际表现，并根据反馈进行调整优化。
* **模块化设计**: 保持各部分（如 `BASE_PERSONA_PROMPT`, `ROLE_SYSTEM_PROMPTS`, `ACTION_PROMPT_TEMPLATES`）的模块化，便于单独更新和维护。

## 6. 附录：Prompt 示例 (完整结构预览)

```

你是一名经验丰富的狼人杀AI玩家... (全局设定)

重要提示 (防误导与决策辅助): ...

--- 游戏背景与规则 ---

你正在参与一场9人标准局的狼人杀游戏... (游戏背景)

关键游戏规则摘要：... (规则概要)

--- 你的角色、目标与技能 ---

* 你的代号是：【AI玩家1】
* 你在本局游戏中的身份是：【平民】 (角色信息)
  作为一名【平民】，你的核心目标是：... (角色目标与技能)

--- 当前游戏状态与历史信息 ---

* 当前阶段：第 2 回合，白天发言 阶段。 (上下文信息)
* 存活玩家列表：[AI玩家1, 玩家B, 玩家C, 狼人A, 狼人B, 女巫, 预言家]
  游戏历史概要:
  第1夜: 玩家X被刀。女巫未使用解药。预言家查验玩家Y为好人。
  第1天: ... (发言与投票历史)

--- 当前任务与行动指引 ---

现在是白天发言阶段。轮到你发言。 (行动指引)

你的角色是【平民】。

当前存活玩家: [AI玩家1, 玩家B, 玩家C, 狼人A, 狼人B, 女巫, 预言家]

游戏历史概要 (包括夜晚信息、过往发言和投票):

(同上)

你的任务: 根据你的角色、已知信息和场上局势，进行你的发言。

你需要努力达成你阵营的目标，说服其他玩家。

发言限制: 240汉字。

请直接输出你的发言内容。

--- 相关策略提示 --- (策略参考)

* 游戏初期，信息较少，注意观察他人发言逻辑，寻找矛盾点。
* 平民要仔细听取每个人的发言，分析逻辑。

--- 输出格式要求 --- (输出要求)

请严格按照【当前任务与行动指引】部分中针对你行动的说明，直接输出你的选择或发言内容。

...

```

---
本文档将作为狼人杀 AI Prompt 设计和开发的主要依据。
```
