from agent_build_sdk.model.roles import ROLE_WOLF
from agent_build_sdk.model.werewolf_model import <PERSON><PERSON><PERSON><PERSON>, AgentReq, STATUS_START, STATUS_WOLF_SPEECH, \
    STATUS_VOTE_RESULT, STATUS_SKILL, STATUS_SKILL_RESULT, STATUS_NIGHT_INFO, STATUS_DAY, STATUS_DISCUSS, STATUS_VOTE, \
    STATUS_RESULT, STATUS_NIGHT, STATUS_SKILL
from agent_build_sdk.utils.logger import logger
from agent_build_sdk.sdk.role_agent import BasicRoleAgent
from common.safe_format import format_prompt
from wolf.prompt import (
    DESC_PROMPT, VOTE_PROMPT, KILL_PROMPT, WOLF_SPEECH_PROMPT,
    BASE_PERSONA_PROMPT, GAME_RULES_SUMMARY, WOLF_ROLE_PROMPT,
    ACTION_TEMPLATES, STRATEGY_TEMPLATES, OUTPUT_TEMPLATES,
    build_prompt
)
from common.werewolf_knowledge import WEREWOLF_TERMINOLOGY, SIX_PLAYER_GAME_FEATURES, ADVANCED_TACTICS


class WolfAgent(BasicRoleAgent):
    """狼人角色Agent"""

    def __init__(self, model_name):
        super().__init__(ROLE_WOLF, model_name=model_name)
        self.memory.set_variable("teammates", [])  # 存储队友信息
        # 默认策略类型，可以根据需要动态调整
        self.strategy_type = "aggressive"  # 可选: "newbie", "aggressive", "master", "rhythm", "lowkey"

    def set_strategy(self, strategy_type):
        """设置狼人的策略类型"""
        if strategy_type in ["newbie", "aggressive", "master", "rhythm", "lowkey"]:
            self.strategy_type = strategy_type
        else:
            logger.warning(f"未知的策略类型: {strategy_type}, 使用默认策略 'aggressive'")
            self.strategy_type = "aggressive"

    def perceive(self, req=AgentReq):
        if req.status == STATUS_START:
            self.memory.clear()
            self.memory.set_variable("name", req.name)
            self.memory.set_variable("teammates", [])  # 重置队友信息
            self.memory.append_history("薄伽丘：游戏开始，这是一场6人局狼人杀游戏，包括预言家、女巫、猎人、平民和狼人")
            self.memory.append_history(f"薄伽丘：{req.name}被分配到的角色是[狼人]")
            if req.message:  # 如果有队友信息
                teammates = req.message.split(",")
                self.memory.set_variable("teammates", teammates)
                self.memory.append_history(f"薄伽丘：{req.name}的狼人队友是: {req.message}")
        elif req.status == STATUS_NIGHT:
            self.memory.append_history("薄伽丘：夜晚降临，所有玩家闭眼进入夜晚阶段")
        elif req.status == STATUS_WOLF_SPEECH:
            # 狼人之间的交流
            if req.name:
                self.memory.append_history(f"薄伽丘：狼人{req.name}对队友说: {req.message}")
            else:
                self.memory.append_history("薄伽丘：狼人睁眼，开始狼人内部交流阶段")
        elif req.status == STATUS_SKILL_RESULT:
            self.memory.append_history(f"薄伽丘：狼人{self.memory.load_variable('name')}选择击杀目标：{req.name}")
        elif req.status == STATUS_NIGHT_INFO:
            self.memory.append_history(f"薄伽丘：天亮了！昨夜的事件：{req.message}")
        elif req.status == STATUS_DISCUSS:  # 发言环节
            if req.name:
                # 其他玩家发言
                self.memory.append_history(f"薄伽丘：{req.name}发言：{req.message}")
            else:
                # 可信发言
                self.memory.append_history(f'薄伽丘：第{req.round}天白天开始，进入发言阶段')
                self.memory.append_history('薄伽丘：每个玩家按顺序描述自己的信息和分析')
        elif req.status == STATUS_VOTE:  # 投票环节
            self.memory.append_history(f"薄伽丘：{req.name}投票：{req.message}")
        elif req.status == STATUS_VOTE_RESULT:  # 投票环节
            out_player = req.name if req.name else req.message
            if out_player:
                self.memory.append_history(f'薄伽丘：投票结果公布，{out_player}被投票出局')
            else:
                self.memory.append_history('薄伽丘：投票结果公布，无人出局')
        elif req.status == STATUS_RESULT:
            self.memory.append_history(f"薄伽丘：游戏结束，{req.message}")
        else:
            raise NotImplementedError

    def interact(self, req=AgentReq) -> AgentResp:
        logger.info("wolf interact: {}".format(req))
        if req.status == STATUS_DISCUSS:
            if req.message:
                self.memory.append_history(req.message)
            teammates = self.memory.load_variable("teammates")
            # 使用新的模块化Prompt构建
            prompt = format_prompt(DESC_PROMPT,
                                  {"base_persona": BASE_PERSONA_PROMPT,
                                   "game_rules": GAME_RULES_SUMMARY + "\n\n" + SIX_PLAYER_GAME_FEATURES + "\n\n" + WEREWOLF_TERMINOLOGY,
                                   "wolf_role": WOLF_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                   "action_discuss": ACTION_TEMPLATES["discuss"],
                                   "strategy": STRATEGY_TEMPLATES[self.strategy_type] + "\n\n" + ADVANCED_TACTICS["wolf"],
                                   "output_discuss": OUTPUT_TEMPLATES["discuss"],
                                   "name": self.memory.load_variable("name"),
                                   "teammates": teammates,
                                   "history": "\n".join(self.memory.load_history())
                                  })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("wolf interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_VOTE:
            self.memory.append_history('薄伽丘：发言结束，现在进入投票阶段，每个人投票选择认为是狼人的玩家')
            teammates = self.memory.load_variable("teammates")
            choices = [name for name in req.message.split(",")
                      if name != self.memory.load_variable("name") and name not in teammates]  # 排除自己和队友
            self.memory.set_variable("choices", choices)
            prompt = format_prompt(VOTE_PROMPT, {"base_persona": BASE_PERSONA_PROMPT,
                                               "game_rules": GAME_RULES_SUMMARY,
                                               "wolf_role": WOLF_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                                               "action_vote": ACTION_TEMPLATES["vote"].format(choices=choices),
                                               "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                                               "output_vote": OUTPUT_TEMPLATES["vote"],
                                               "name": self.memory.load_variable("name"),
                                               "teammates": teammates,
                                               "choices": choices,
                                               "history": "\n".join(self.memory.load_history())
                                              })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("wolf interact result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_WOLF_SPEECH:
            teammates = self.memory.load_variable("teammates")
            prompt = format_prompt(WOLF_SPEECH_PROMPT, {
                "base_persona": BASE_PERSONA_PROMPT,
                "game_rules": GAME_RULES_SUMMARY,
                "wolf_role": WOLF_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                "action_wolf_speech": ACTION_TEMPLATES["wolf_speech"],
                "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                "output_wolf_speech": OUTPUT_TEMPLATES["wolf_speech"],
                "name": self.memory.load_variable("name"),
                "teammates": teammates,
                "history": "\n".join(self.memory.load_history())
            })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("wolf speech result: {}".format(result))
            return AgentResp(success=True, result=result, errMsg=None)

        elif req.status == STATUS_SKILL:
            teammates = self.memory.load_variable("teammates")
            choices = [name for name in req.message.split(",")
                      if name != self.memory.load_variable("name") and name not in teammates]  # 排除自己和队友
            self.memory.set_variable("choices", choices)
            prompt = format_prompt(KILL_PROMPT, {
                "base_persona": BASE_PERSONA_PROMPT,
                "game_rules": GAME_RULES_SUMMARY,
                "wolf_role": WOLF_ROLE_PROMPT.format(name=self.memory.load_variable("name")),
                "action_kill": ACTION_TEMPLATES["kill"].format(choices=choices),
                "strategy": STRATEGY_TEMPLATES[self.strategy_type],
                "output_kill": OUTPUT_TEMPLATES["kill"],
                "name": self.memory.load_variable("name"),
                "choices": choices,
                "history": "\n".join(self.memory.load_history())
            })
            logger.info("prompt:" + prompt)
            result = self.llm_caller(prompt)
            logger.info("wolf kill result: {}".format(result))
            return AgentResp(success=True, result=result, skillTargetPlayer=result, serrMsg=None)
        else:
            raise NotImplementedError